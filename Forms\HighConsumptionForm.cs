using System;
using System.Windows.Forms;
using System.Collections.Generic;
using ElectricityBillingSystem.Models;
using ElectricityBillingSystem.DataAccess;

namespace ElectricityBillingSystem.Forms
{
    public partial class HighConsumptionForm : Form
    {
        private DatabaseHelper dbHelper = new DatabaseHelper();
        
        public HighConsumptionForm()
        {
            InitializeComponent();
            LoadHighConsumptionCustomers();
        }
        
        private void LoadHighConsumptionCustomers()
        {
            // Get customers with high consumption
            List<Customer> highConsumptionCustomers = dbHelper.GetHighConsumptionCustomers();
            dataGridViewHighConsumption.DataSource = highConsumptionCustomers;
        }
        
        private void btnExport_Click(object sender, EventArgs e)
        {
            // Export data to Excel or PDF
            ExportData();
        }
        
        private void ExportData()
        {
            // Implement export functionality
        }
    }
}