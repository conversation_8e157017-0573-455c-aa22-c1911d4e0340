using System;
using System.Windows.Forms;
using ElectricityBillingSystem.Models;

namespace ElectricityBillingSystem.Forms
{
    public partial class CustomerDetailsForm : Form
    {
        private Customer customer;
        
        public CustomerDetailsForm(Customer customer)
        {
            InitializeComponent();
            this.customer = customer;
            LoadCustomerData();
        }
        
        private void LoadCustomerData()
        {
            // Display customer information in form controls
            txtName.Text = customer.NAME_A;
            txtAccountNumber.Text = customer.ACCTNO;
            txtMeterNumber.Text = customer.MATER_NO;
            txtAddress.Text = customer.ADRESS;
            // Load other fields...
            
            // Calculate consumption
            double consumption = customer.LAST_READ - customer.PREV_READ;
            txtConsumption.Text = consumption.ToString("N2");
        }
        
        private void btnPrint_Click(object sender, EventArgs e)
        {
            // Print customer details
            PrintCustomerDetails();
        }
        
        private void btnCopy_Click(object sender, EventArgs e)
        {
            // Copy customer details to clipboard
            string customerInfo = $"رقم الحساب: {customer.ACCTNO}\r\n" +
                                 $"الاسم: {customer.NAME_A}\r\n" +
                                 $"رقم المقياس: {customer.MATER_NO}\r\n" +
                                 $"العنوان: {customer.ADRESS}";
            
            Clipboard.SetText(customerInfo);
            MessageBox.Show("تم نسخ بيانات المشترك", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        
        private void PrintCustomerDetails()
        {
            // Implement printing functionality
        }
    }
}