using System;
using System.Windows.Forms;
using System.Collections.Generic;
using ElectricityBillingSystem.Models;
using ElectricityBillingSystem.DataAccess;

namespace ElectricityBillingSystem.Forms
{
    public partial class CustomerSearchForm : Form
    {
        private DatabaseHelper dbHelper = new DatabaseHelper();
        
        public CustomerSearchForm()
        {
            InitializeComponent();
            SetupSearchControls();
        }
        
        private void SetupSearchControls()
        {
            // Setup radio buttons for search type
            radioName.Checked = true;
            
            // Setup data grid view
            dataGridViewResults.AutoGenerateColumns = false;
            // Add columns...
        }
        
        private void btnSearch_Click(object sender, EventArgs e)
        {
            string searchText = txtSearch.Text.Trim();
            
            if (string.IsNullOrEmpty(searchText))
            {
                MessageBox.Show("الرجاء إدخال نص للبحث", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            List<Customer> results = new List<Customer>();
            
            if (radioName.Checked)
            {
                results = dbHelper.SearchCustomersByName(searchText);
            }
            else if (radioAccountNumber.Checked)
            {
                results = dbHelper.SearchCustomersByAccountNumber(searchText);
            }
            else if (radioMeterNumber.Checked)
            {
                results = dbHelper.SearchCustomersByMeterNumber(searchText);
            }
            
            dataGridViewResults.DataSource = results;
        }
        
        private void dataGridViewResults_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                Customer selectedCustomer = (Customer)dataGridViewResults.Rows[e.RowIndex].DataBoundItem;
                CustomerDetailsForm detailsForm = new CustomerDetailsForm(selectedCustomer);
                detailsForm.ShowDialog();
            }
        }
    }
}