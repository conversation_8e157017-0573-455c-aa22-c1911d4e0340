using System;

namespace ElectricityBillingSystem.Models
{
    public class Customer
    {
        public string ACCTNO { get; set; } = "";
        public string INSTALL_NO { get; set; } = "";
        public string SERIAL { get; set; } = "";
        public string NAME_A { get; set; } = "";
        public string HOUSE_NO { get; set; } = "";
        public string ADRESS { get; set; } = "";
        public string MATER_NO { get; set; } = "";
        public string MPHASE { get; set; } = "";
        public double METER_FACT { get; set; }
        public double LAST_READ { get; set; }
        public DateTime LAST_DATE { get; set; }
        public double PREV_READ { get; set; }
        public DateTime PREV_DATE { get; set; }
        public double METER_RENT { get; set; }
        public double CB_RENT { get; set; }
        public double OTHCHARGE { get; set; }
        public double OUTS { get; set; }
        public double BKOUTS { get; set; }
        public string HOUSE_COD { get; set; } = "";
        public bool EVEN_CLOSE { get; set; }
        public double PAYMENT { get; set; }
        public DateTime PAY_DATE { get; set; }
        public DateTime BILL_DATE { get; set; }
        public double OLD_EXCH { get; set; }
    }
}