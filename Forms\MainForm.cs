using System;
using System.Windows.Forms;

namespace ElectricityBillingSystem.Forms
{
    public partial class MainForm : Form
    {
        public MainForm()
        {
            InitializeComponent();
            CustomizeDesign();
        }
        
        private void CustomizeDesign()
        {
            // Add Iraqi Electricity Ministry logo
            // pictureBoxLogo.Image = Properties.Resources.ElectricityLogo;

            // Set form icon
            // this.Icon = Properties.Resources.AppIcon;

            // For now, just set a placeholder
            pictureBoxLogo.BackColor = System.Drawing.Color.LightBlue;
        }
        
        private void btnCustomerSearch_Click(object sender, EventArgs e)
        {
            CustomerSearchForm searchForm = new CustomerSearchForm();
            searchForm.ShowDialog();
        }
        
        private void btnCustomerManagement_Click(object sender, EventArgs e)
        {
            CustomerManagementForm managementForm = new CustomerManagementForm();
            managementForm.ShowDialog();
        }
        
        private void btnHighConsumption_Click(object sender, EventArgs e)
        {
            HighConsumptionForm highConsumptionForm = new HighConsumptionForm();
            highConsumptionForm.ShowDialog();
        }
        
        private void btnReports_Click(object sender, EventArgs e)
        {
            ReportsForm reportsForm = new ReportsForm();
            reportsForm.ShowDialog();
        }
    }
}