using System;
using System.Windows.Forms;
using System.Collections.Generic;
using ElectricityBillingSystem.Models;
using ElectricityBillingSystem.DataAccess;

namespace ElectricityBillingSystem.Forms
{
    public partial class ReportsForm : Form
    {
        private DatabaseHelper dbHelper = new DatabaseHelper();
        
        public ReportsForm()
        {
            InitializeComponent();
            LoadReports();
        }
        
        private void LoadReports()
        {
            // Load different types of reports
            List<Customer> allCustomers = dbHelper.GetAllCustomers();
            dataGridViewReports.DataSource = allCustomers;
        }
        
        private void btnGenerateReport_Click(object sender, EventArgs e)
        {
            // Generate selected report
            GenerateReport();
        }
        
        private void GenerateReport()
        {
            // Implement report generation
            MessageBox.Show("تم إنشاء التقرير بنجاح", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        
        private void btnExportReport_Click(object sender, EventArgs e)
        {
            // Export report to file
            ExportReport();
        }
        
        private void ExportReport()
        {
            // Implement export functionality
            MessageBox.Show("تم تصدير التقرير بنجاح", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
