using System;
using System.Windows.Forms;
using ElectricityBillingSystem.Models;
using ElectricityBillingSystem.DataAccess;

namespace ElectricityBillingSystem.Forms
{
    public partial class CustomerManagementForm : Form
    {
        private DatabaseHelper dbHelper = new DatabaseHelper();
        private Customer currentCustomer;
        
        public CustomerManagementForm()
        {
            InitializeComponent();
            SetupForm();
        }
        
        private void SetupForm()
        {
            // Initialize form controls
            ClearForm();
            LoadCustomers();
        }
        
        private void LoadCustomers()
        {
            dataGridViewCustomers.DataSource = dbHelper.GetAllCustomers();
        }
        
        private void ClearForm()
        {
            txtName.Text = string.Empty;
            txtAccountNumber.Text = string.Empty;
            txtMeterNumber.Text = string.Empty;
            // Clear other fields...
            
            currentCustomer = null;
            btnSave.Text = "إضافة";
        }
        
        private void btnSave_Click(object sender, EventArgs e)
        {
            if (ValidateForm())
            {
                if (currentCustomer == null)
                {
                    // Add new customer
                    Customer newCustomer = new Customer
                    {
                        NAME_A = txtName.Text,
                        ACCTNO = txtAccountNumber.Text,
                        MATER_NO = txtMeterNumber.Text,
                        // Set other properties...
                    };
                    
                    dbHelper.AddCustomer(newCustomer);
                }
                else
                {
                    // Update existing customer
                    currentCustomer.NAME_A = txtName.Text;
                    currentCustomer.ACCTNO = txtAccountNumber.Text;
                    currentCustomer.MATER_NO = txtMeterNumber.Text;
                    // Update other properties...
                    
                    dbHelper.UpdateCustomer(currentCustomer);
                }
                
                LoadCustomers();
                ClearForm();
            }
        }
        
        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (currentCustomer != null)
            {
                if (MessageBox.Show("هل أنت متأكد من حذف هذا المشترك؟", "تأكيد الحذف", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    dbHelper.DeleteCustomer(currentCustomer.ACCTNO);
                    LoadCustomers();
                    ClearForm();
                }
            }
        }
        
        private bool ValidateForm()
        {
            // Validate form inputs
            if (string.IsNullOrEmpty(txtName.Text) || string.IsNullOrEmpty(txtAccountNumber.Text))
            {
                MessageBox.Show("الرجاء إدخال البيانات المطلوبة", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }
            
            return true;
        }
    }
}