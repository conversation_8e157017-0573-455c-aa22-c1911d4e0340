using System;
using System.Data;
using System.Data.OleDb;
using System.Collections.Generic;
using ElectricityBillingSystem.Models;

namespace ElectricityBillingSystem.DataAccess
{
    public class DatabaseHelper
    {
        private string connectionString = @"Provider=Microsoft.ACE.OLEDB.12.0;Data Source=DataAccess\ElectricityBilling.accdb";
        
        public List<Customer> GetAllCustomers()
        {
            List<Customer> customers = new List<Customer>();
            
            using (OleDbConnection connection = new OleDbConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Customers";
                
                using (OleDbCommand command = new OleDbCommand(query, connection))
                {
                    using (OleDbDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            customers.Add(MapCustomer(reader));
                        }
                    }
                }
            }
            
            return customers;
        }
        
        public List<Customer> SearchCustomersByName(string name)
        {
            List<Customer> customers = new List<Customer>();

            using (OleDbConnection connection = new OleDbConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Customers WHERE NAME_A LIKE ?";

                using (OleDbCommand command = new OleDbCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@name", "%" + name + "%");
                    using (OleDbDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            customers.Add(MapCustomer(reader));
                        }
                    }
                }
            }

            return customers;
        }

        public List<Customer> SearchCustomersByAccountNumber(string accountNumber)
        {
            List<Customer> customers = new List<Customer>();

            using (OleDbConnection connection = new OleDbConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Customers WHERE ACCTNO LIKE ?";

                using (OleDbCommand command = new OleDbCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@acctno", "%" + accountNumber + "%");
                    using (OleDbDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            customers.Add(MapCustomer(reader));
                        }
                    }
                }
            }

            return customers;
        }

        public List<Customer> SearchCustomersByMeterNumber(string meterNumber)
        {
            List<Customer> customers = new List<Customer>();

            using (OleDbConnection connection = new OleDbConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Customers WHERE MATER_NO LIKE ?";

                using (OleDbCommand command = new OleDbCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@mater_no", "%" + meterNumber + "%");
                    using (OleDbDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            customers.Add(MapCustomer(reader));
                        }
                    }
                }
            }

            return customers;
        }

        public List<Customer> GetHighConsumptionCustomers()
        {
            List<Customer> customers = new List<Customer>();

            using (OleDbConnection connection = new OleDbConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Customers WHERE (LAST_READ - PREV_READ) > 500 ORDER BY (LAST_READ - PREV_READ) DESC";

                using (OleDbCommand command = new OleDbCommand(query, connection))
                {
                    using (OleDbDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            customers.Add(MapCustomer(reader));
                        }
                    }
                }
            }

            return customers;
        }

        public void AddCustomer(Customer customer)
        {
            using (OleDbConnection connection = new OleDbConnection(connectionString))
            {
                connection.Open();
                string query = @"INSERT INTO Customers (ACCTNO, INSTALL_NO, SERIAL, NAME_A, HOUSE_NO, ADRESS, MATER_NO, MPHASE,
                                METER_FACT, LAST_READ, LAST_DATE, PREV_READ, PREV_DATE, METER_RENT, CB_RENT, OTHCHARGE,
                                OUTS, BKOUTS, HOUSE_COD, EVEN_CLOSE, PAYMENT, PAY_DATE, BILL_DATE, OLD_EXCH)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                using (OleDbCommand command = new OleDbCommand(query, connection))
                {
                    AddCustomerParameters(command, customer);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void UpdateCustomer(Customer customer)
        {
            using (OleDbConnection connection = new OleDbConnection(connectionString))
            {
                connection.Open();
                string query = @"UPDATE Customers SET INSTALL_NO=?, SERIAL=?, NAME_A=?, HOUSE_NO=?, ADRESS=?, MATER_NO=?,
                                MPHASE=?, METER_FACT=?, LAST_READ=?, LAST_DATE=?, PREV_READ=?, PREV_DATE=?, METER_RENT=?,
                                CB_RENT=?, OTHCHARGE=?, OUTS=?, BKOUTS=?, HOUSE_COD=?, EVEN_CLOSE=?, PAYMENT=?, PAY_DATE=?,
                                BILL_DATE=?, OLD_EXCH=? WHERE ACCTNO=?";

                using (OleDbCommand command = new OleDbCommand(query, connection))
                {
                    AddCustomerParameters(command, customer, false);
                    command.Parameters.AddWithValue("@acctno", customer.ACCTNO);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void DeleteCustomer(string accountNumber)
        {
            using (OleDbConnection connection = new OleDbConnection(connectionString))
            {
                connection.Open();
                string query = "DELETE FROM Customers WHERE ACCTNO = ?";

                using (OleDbCommand command = new OleDbCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@acctno", accountNumber);
                    command.ExecuteNonQuery();
                }
            }
        }

        private Customer MapCustomer(OleDbDataReader reader)
        {
            Customer customer = new Customer();

            try
            {
                customer.ACCTNO = reader["ACCTNO"]?.ToString() ?? "";
                customer.INSTALL_NO = reader["INSTALL_NO"]?.ToString() ?? "";
                customer.SERIAL = reader["SERIAL"]?.ToString() ?? "";
                customer.NAME_A = reader["NAME_A"]?.ToString() ?? "";
                customer.HOUSE_NO = reader["HOUSE_NO"]?.ToString() ?? "";
                customer.ADRESS = reader["ADRESS"]?.ToString() ?? "";
                customer.MATER_NO = reader["MATER_NO"]?.ToString() ?? "";
                customer.MPHASE = reader["MPHASE"]?.ToString() ?? "";
                customer.METER_FACT = Convert.ToDouble(reader["METER_FACT"] ?? 0);
                customer.LAST_READ = Convert.ToDouble(reader["LAST_READ"] ?? 0);
                customer.LAST_DATE = Convert.ToDateTime(reader["LAST_DATE"] ?? DateTime.Now);
                customer.PREV_READ = Convert.ToDouble(reader["PREV_READ"] ?? 0);
                customer.PREV_DATE = Convert.ToDateTime(reader["PREV_DATE"] ?? DateTime.Now);
                customer.METER_RENT = Convert.ToDouble(reader["METER_RENT"] ?? 0);
                customer.CB_RENT = Convert.ToDouble(reader["CB_RENT"] ?? 0);
                customer.OTHCHARGE = Convert.ToDouble(reader["OTHCHARGE"] ?? 0);
                customer.OUTS = Convert.ToDouble(reader["OUTS"] ?? 0);
                customer.BKOUTS = Convert.ToDouble(reader["BKOUTS"] ?? 0);
                customer.HOUSE_COD = reader["HOUSE_COD"]?.ToString() ?? "";
                customer.EVEN_CLOSE = Convert.ToBoolean(reader["EVEN_CLOSE"] ?? false);
                customer.PAYMENT = Convert.ToDouble(reader["PAYMENT"] ?? 0);
                customer.PAY_DATE = Convert.ToDateTime(reader["PAY_DATE"] ?? DateTime.Now);
                customer.BILL_DATE = Convert.ToDateTime(reader["BILL_DATE"] ?? DateTime.Now);
                customer.OLD_EXCH = Convert.ToDouble(reader["OLD_EXCH"] ?? 0);
            }
            catch (Exception ex)
            {
                // Handle mapping errors gracefully
                System.Diagnostics.Debug.WriteLine($"Error mapping customer: {ex.Message}");
            }

            return customer;
        }

        private void AddCustomerParameters(OleDbCommand command, Customer customer, bool includeAccountNumber = true)
        {
            if (includeAccountNumber)
                command.Parameters.AddWithValue("@acctno", customer.ACCTNO ?? "");

            command.Parameters.AddWithValue("@install_no", customer.INSTALL_NO ?? "");
            command.Parameters.AddWithValue("@serial", customer.SERIAL ?? "");
            command.Parameters.AddWithValue("@name_a", customer.NAME_A ?? "");
            command.Parameters.AddWithValue("@house_no", customer.HOUSE_NO ?? "");
            command.Parameters.AddWithValue("@adress", customer.ADRESS ?? "");
            command.Parameters.AddWithValue("@mater_no", customer.MATER_NO ?? "");
            command.Parameters.AddWithValue("@mphase", customer.MPHASE ?? "");
            command.Parameters.AddWithValue("@meter_fact", customer.METER_FACT);
            command.Parameters.AddWithValue("@last_read", customer.LAST_READ);
            command.Parameters.AddWithValue("@last_date", customer.LAST_DATE);
            command.Parameters.AddWithValue("@prev_read", customer.PREV_READ);
            command.Parameters.AddWithValue("@prev_date", customer.PREV_DATE);
            command.Parameters.AddWithValue("@meter_rent", customer.METER_RENT);
            command.Parameters.AddWithValue("@cb_rent", customer.CB_RENT);
            command.Parameters.AddWithValue("@othcharge", customer.OTHCHARGE);
            command.Parameters.AddWithValue("@outs", customer.OUTS);
            command.Parameters.AddWithValue("@bkouts", customer.BKOUTS);
            command.Parameters.AddWithValue("@house_cod", customer.HOUSE_COD ?? "");
            command.Parameters.AddWithValue("@even_close", customer.EVEN_CLOSE);
            command.Parameters.AddWithValue("@payment", customer.PAYMENT);
            command.Parameters.AddWithValue("@pay_date", customer.PAY_DATE);
            command.Parameters.AddWithValue("@bill_date", customer.BILL_DATE);
            command.Parameters.AddWithValue("@old_exch", customer.OLD_EXCH);
        }
    }
}